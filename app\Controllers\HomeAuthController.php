<?php

namespace App\Controllers;

class HomeAuthController extends BaseController
{
    protected $session;
    protected $usersModel;

    public function __construct()
    {
        helper(['form', 'url', 'info']);
        $this->session = session();
        $this->usersModel = new \App\Models\UsersModel();
    }

    // Admin Login Methods
    public function loginForm()
    {
        return view('home/home_login', [
            'title' => 'Admin Login',
            'menu' => 'login'
        ]);
    }

    public function processLogin()
    {
        // Validate input
        $rules = [
            'username' => 'required',
            'password' => 'required'
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->with('error', 'Username and password are required');
        }

        $username = $this->request->getPost('username');
        $password = $this->request->getPost('password');

        try {
            // Find user by username
            $user = $this->usersModel->findByUsername($username);

            if ($user && password_verify($password, $user['password'])) {
                // Check if user is active
                if ($user['status'] !== '1') {
                    return redirect()->back()->with('error', 'Your account is inactive. Please contact administrator.');
                }

                // Check if user has admin role
                if (!in_array($user['role'], ['admin', 'supervisor', 'user'])) {
                    return redirect()->back()->with('error', 'Access denied. Admin privileges required.');
                }

                // Set session data
                $this->session->set([
                    'logged_in' => true,
                    'user_id' => $user['id'],
                    'name' => $user['name'],
                    'username' => $user['username'],
                    'role' => $user['role'],
                    'org_id' => $user['org_id'],
                    'orgcode' => $user['orgcode'],
                    'position' => $user['position'],
                    'email' => $user['email']
                ]);

                return redirect()->to('dashboard')->with('success', "Welcome back, {$user['name']}! You've successfully logged in.");
            } else {
                return redirect()->back()->with('error', 'Invalid username or password. Please try again.');
            }
        } catch (\Exception $e) {
            log_message('error', 'Login error: ' . $e->getMessage());
            return redirect()->back()->with('error', 'An error occurred during login. Please try again.');
        }
    }

    // Admin Logout Method
    public function adminLogout()
    {
        // Destroy the admin's session
        $this->session->destroy();

        return redirect()->to('login')->with('success', 'You have been logged out successfully.');
    }

    // Applicant Registration Methods
    public function registerForm()
    {
        return view('home/home_register', [
            'title' => 'Create Account',
            'menu' => 'register'
        ]);
    }

    public function processRegister()
    {
        $rules = [
            'firstname' => 'required|min_length[2]|max_length[50]',
            'lastname' => 'required|min_length[2]|max_length[50]',
            'email' => 'required|valid_email|is_unique[applicants.email]',
            'password' => 'required|min_length[4]',
            'confirm_password' => 'required|matches[password]'
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        // Load the ApplicantsModel
        $applicantsModel = new \App\Models\ApplicantsModel();

        // Check if email already exists (double-check server-side)
        $existingApplicant = $applicantsModel->getApplicantByEmail($this->request->getPost('email'));
        if ($existingApplicant) {
            return redirect()->back()->withInput()->with('error', 'This email address is already registered. Please use a different email or try logging in.');
        }

        // Generate unique ID and activation token
        $unique_id = 'APP' . time() . rand(1000, 9999);
        $activation_token = bin2hex(random_bytes(32));

        // Prepare data for insertion
        $data = [
            'unique_id' => $unique_id,
            'first_name' => $this->request->getPost('firstname'),
            'last_name' => $this->request->getPost('lastname'),
            'email' => $this->request->getPost('email'),
            'password' => $this->request->getPost('password'), // Model will hash this
            'activation_token' => $activation_token,
            'status' => 0 // Inactive until activated
        ];

        try {
            // Save applicant data
            $inserted = $applicantsModel->insert($data);

            if (!$inserted) {
                log_message('error', 'Failed to insert applicant data: ' . json_encode($applicantsModel->errors()));
                return redirect()->back()->withInput()->with('error', 'Failed to create account. Please try again.');
            }

            // Send activation email
            $this->sendActivationEmail($data['email'], $data['first_name'], $activation_token);

            session()->setFlashdata('swal_icon', 'success');
            session()->setFlashdata('swal_title', 'Account Created');
            session()->setFlashdata('swal_text', 'Your account has been created successfully. Please check your email for activation instructions.');

            return redirect()->to('/');

        } catch (\Exception $e) {
            log_message('error', 'Registration error: ' . $e->getMessage());
            return redirect()->back()->withInput()->with('error', 'An error occurred during registration. Please try again.');
        }
    }

    /**
     * Check if email already exists in database
     * AJAX endpoint for email verification
     */
    public function checkEmailAvailability()
    {
        // Only allow POST requests
        if (!$this->request->isAJAX()) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Invalid request method'
            ]);
        }

        $email = $this->request->getPost('email');

        if (!$email) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Email is required'
            ]);
        }

        // Validate email format
        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Invalid email format'
            ]);
        }

        // Load the ApplicantsModel
        $applicantsModel = new \App\Models\ApplicantsModel();

        // Check if email exists
        $existingApplicant = $applicantsModel->getApplicantByEmail($email);

        if ($existingApplicant) {
            return $this->response->setJSON([
                'success' => false,
                'available' => false,
                'message' => 'This email address is already registered'
            ]);
        } else {
            return $this->response->setJSON([
                'success' => true,
                'available' => true,
                'message' => 'Email address is available'
            ]);
        }
    }

    // Applicant Login Methods
    public function applicantLoginForm()
    {
        return view('home/home_applicant_login', [
            'title' => 'Applicant Login',
            'menu' => 'applicant_login'
        ]);
    }

    public function processApplicantLogin()
    {
        $email = $this->request->getPost('email');
        $password = $this->request->getPost('password');

        // Validate input
        if (!$email || !$password) {
            session()->setFlashdata('swal_icon', 'error');
            session()->setFlashdata('swal_title', 'Missing Information');
            session()->setFlashdata('swal_text', 'Please enter both email and password.');
            return redirect()->back();
        }

        // Load the ApplicantsModel
        $applicantsModel = new \App\Models\ApplicantsModel();

        // Verify applicant credentials
        $applicant = $applicantsModel->verifyApplicant($email, $password);

        if (!$applicant) {
            session()->setFlashdata('swal_icon', 'error');
            session()->setFlashdata('swal_title', 'Login Failed');
            session()->setFlashdata('swal_text', 'Invalid email or password. Please try again.');
            return redirect()->back();
        }

        // Check if account is activated (status = 1 means active)
        if ($applicant['status'] != 1) {
            session()->setFlashdata('swal_icon', 'warning');
            session()->setFlashdata('swal_title', 'Account Not Activated');
            session()->setFlashdata('swal_text', 'Your account is not yet activated. Please check your email for activation instructions.');
            return redirect()->back();
        }

        // Set session data with real applicant information
        $this->session->set([
            'logged_in' => true,
            'applicant_id' => $applicant['id'],
            'applicant_unique_id' => $applicant['unique_id'],
            'applicant_name' => trim($applicant['first_name'] . ' ' . $applicant['last_name']),
            'applicant_email' => $applicant['email'],
            'applicant_first_name' => $applicant['first_name'],
            'applicant_last_name' => $applicant['last_name']
        ]);

        session()->setFlashdata('swal_icon', 'success');
        session()->setFlashdata('swal_title', 'Login Successful');
        session()->setFlashdata('swal_text', 'Welcome back, ' . $applicant['first_name'] . '!');
        return redirect()->to('applicant/dashboard');
    }

    // Account Activation
    public function activate($token)
    {
        // Load the ApplicantsModel
        $applicantsModel = new \App\Models\ApplicantsModel();

        // Attempt to activate the account
        $activated = $applicantsModel->activateAccount($token);

        if ($activated) {
            session()->setFlashdata('swal_icon', 'success');
            session()->setFlashdata('swal_title', 'Account Activated');
            session()->setFlashdata('swal_text', 'Your account has been activated successfully. You can now login.');
        } else {
            session()->setFlashdata('swal_icon', 'error');
            session()->setFlashdata('swal_title', 'Activation Failed');
            session()->setFlashdata('swal_text', 'Invalid or expired activation token. Please contact support if you continue to have issues.');
        }

        return redirect()->to('applicant/login');
    }

    // Applicant Logout
    public function applicantLogout()
    {
        // Destroy the applicant's session
        $this->session->destroy();

        session()->setFlashdata('swal_icon', 'success');
        session()->setFlashdata('swal_title', 'Logged Out');
        session()->setFlashdata('swal_text', 'You have been logged out successfully.');

        return redirect()->to('applicant/login');
    }

    // Forgot Password for Applicant
    public function forgotApplicantPassword()
    {
        $email = $this->request->getPost('email');
        if (!$email) {
            session()->setFlashdata('swal_icon', 'error');
            session()->setFlashdata('swal_title', 'Missing Email');
            session()->setFlashdata('swal_text', 'Please enter your registered email address.');
            return redirect()->to('applicant/login');
        }

        $applicantsModel = new \App\Models\ApplicantsModel();
        $applicant = $applicantsModel->getApplicantByEmail($email);

        if (!$applicant) {
            session()->setFlashdata('swal_icon', 'error');
            session()->setFlashdata('swal_title', 'Not Found');
            session()->setFlashdata('swal_text', 'No applicant found with that email address.');
            return redirect()->to('applicant/login');
        }

        // Check if applicant account is deleted
        if (isset($applicant['deleted_at']) && $applicant['deleted_at'] !== null) {
            session()->setFlashdata('swal_icon', 'error');
            session()->setFlashdata('swal_title', 'Account Unavailable');
            session()->setFlashdata('swal_text', 'This account is no longer available.');
            return redirect()->to('applicant/login');
        }

        // Generate a random 4-digit code
        $newPassword = strval(rand(1000, 9999));

        // Update password and activate account if not already active (model will hash password)
        $updateData = [
            'password' => $newPassword
        ];

        // If account is not active, activate it during password reset
        if ($applicant['status'] != 1) {
            $updateData['status'] = 1;
            $updateData['activation_token'] = null;
        }

        $updateResult = $applicantsModel->update($applicant['id'], $updateData);

        if (!$updateResult) {
            log_message('error', 'Failed to update password for applicant ID: ' . $applicant['id']);
            session()->setFlashdata('swal_icon', 'error');
            session()->setFlashdata('swal_title', 'Update Failed');
            session()->setFlashdata('swal_text', 'Failed to update password. Please try again.');
            return redirect()->to('applicant/login');
        }

        // Configure email with explicit settings
        $emailConfig = [
            'protocol' => 'smtp',
            'SMTPHost' => 'mail.dakoiims.com',
            'SMTPUser' => '<EMAIL>',
            'SMTPPass' => 'dakoiianzii',
            'SMTPPort' => 465,
            'SMTPCrypto' => 'ssl',
            'SMTPTimeout' => 30,
            'mailType' => 'html',
            'charset' => 'UTF-8',
            'wordWrap' => true,
            'wrapChars' => 76,
            'validate' => true,
            'priority' => 3,
            'CRLF' => "\r\n",
            'newline' => "\r\n"
        ];

        // Send email
        $emailService = \Config\Services::email();
        $emailService->initialize($emailConfig);
        $emailService->setTo($email);
        $emailService->setFrom('<EMAIL>', 'DERS System');
        $emailService->setSubject('DERS Password Reset');

        $body = view('emails/emails_forgot_password', [
            'firstname' => $applicant['first_name'] ?? 'User',
            'reset_code' => $newPassword
        ]);

        $emailService->setMessage($body);

        try {
            $sent = $emailService->send();

            if ($sent) {
                log_message('info', 'Password reset email sent successfully to: ' . $email);
                session()->setFlashdata('swal_icon', 'success');
                session()->setFlashdata('swal_title', 'Password Reset');
                session()->setFlashdata('swal_text', 'A new password has been sent to your email. Please check your inbox and login.');
            } else {
                log_message('error', 'Failed to send password reset email to: ' . $email);
                log_message('error', 'Email debug info: ' . $emailService->printDebugger(['headers']));
                session()->setFlashdata('swal_icon', 'error');
                session()->setFlashdata('swal_title', 'Email Failed');
                session()->setFlashdata('swal_text', 'Could not send reset email. Please try again later or contact support.');
            }
        } catch (\Exception $e) {
            log_message('error', 'Email exception: ' . $e->getMessage());
            session()->setFlashdata('swal_icon', 'error');
            session()->setFlashdata('swal_title', 'Email Error');
            session()->setFlashdata('swal_text', 'An error occurred while sending the email. Please try again later.');
        }

        return redirect()->to('applicant/login');
    }

    /**
     * Send activation email to new applicant
     *
     * @param string $email
     * @param string $firstname
     * @param string $activation_token
     * @return bool
     */
    private function sendActivationEmail($email, $firstname, $activation_token)
    {
        try {
            // Configure email with explicit settings
            $emailConfig = [
                'protocol' => 'smtp',
                'SMTPHost' => 'mail.dakoiims.com',
                'SMTPUser' => '<EMAIL>',
                'SMTPPass' => 'dakoiianzii',
                'SMTPPort' => 465,
                'SMTPCrypto' => 'ssl',
                'SMTPTimeout' => 30,
                'mailType' => 'html',
                'charset' => 'UTF-8',
                'wordWrap' => true,
                'wrapChars' => 76,
                'validate' => true,
                'priority' => 3,
                'CRLF' => "\r\n",
                'newline' => "\r\n"
            ];

            // Send email
            $emailService = \Config\Services::email();
            $emailService->initialize($emailConfig);
            $emailService->setTo($email);
            $emailService->setFrom('<EMAIL>', 'DERS System');
            $emailService->setSubject('Activate Your DERS Account');

            $activation_link = base_url("applicant/activate/{$activation_token}");
            $body = view('emails/emails_activation_email', [
                'firstname' => $firstname,
                'activation_link' => $activation_link
            ]);

            $emailService->setMessage($body);

            $emailSent = $emailService->send();

            if (!$emailSent) {
                log_message('error', 'Activation email failed to send: ' . $emailService->printDebugger(['headers']));
                return false;
            }

            log_message('info', 'Activation email sent successfully to: ' . $email);
            return true;

        } catch (\Exception $e) {
            log_message('error', 'Activation email exception: ' . $e->getMessage());
            return false;
        }
    }
}
